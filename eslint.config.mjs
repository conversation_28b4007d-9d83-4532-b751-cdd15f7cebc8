// References:
// - https://github.com/antfu/eslint-config

import antfu from "@antfu/eslint-config";

export default antfu(
  // Base configuration
  {
    type: "app",
    typescript: true,
    formatters: true,
    json: true,
    jsonc: true,
    markdown: false,
    gitignore: true,
    json5: true,
    xml: false,
    html: false,
    css: false,
    scss: false,
    less: false,
    sass: false,
    graphql: false,
    vue: false,
    yaml: false,
    jsx: false,
    tsx: false,
    test: true,
    toml: false,
    stylistic: {
      indent: 2,
      semi: true,
      quotes: "double",
      arrowParens: "always",
      trailingComma: "all",
      bracketSpacing: true,
      singleQuote: false,
      tabWidth: 2,
      useTabs: false,
      endOfLine: "auto",
      printWidth: 80,
      proseWrap: "preserve",
      htmlWhitespaceSensitivity: "ignore",
      vueIndentScriptAndStyle: false,
      embeddedLanguageFormatting: "auto",
      quoteProps: "as-needed",
      jsxSingleQuote: false,
      jsxBracketSameLine: false,
      requireConfig: false,
      noMixedSpacesAndTabs: true,
      noTrailingSpaces: true,
      noExtraBlankLines: true,
      noExtraParens: true,
      noEmptyLines: true,
      noEmptyBlocks: true,
      noEmptyFunctions: true,
      noEmptyStatements: true,
      noEmptyCatch: true,
      noEmptyInterfaces: true,
      noEmptyTypes: true,
      noEmptyObject: false,
      noEmptyArray: false,
      noEmptyTuple: false,
    },
    ignores: [
      "**/node_modules/**",
      "**/dist/**",
      "**/build/**",
      "**/coverage/**",
      "**/out/**",
      "**/tasks/**",
      "**/vitest.config.ts",
    ],
  },
  // General rules override
  {
    rules: {
      "sort-keys": "off",
      "ts/no-redeclare": "off",
      "ts/consistent-type-definitions": ["error", "type"],
      "no-console": ["warn", {
        allow: ["error", "warn", "info", "debug"],
      }],
      "antfu/no-top-level-await": ["off"],
      "node/prefer-global/process": ["off"],
      "node/no-process-env": ["error"],
      "perfectionist/sort-imports": ["error"],
      "perfectionist/sort-exports": ["error"],
      "perfectionist/sort-objects": ["error", {
        type: "alphabetical",
        order: "asc",
      }],
      "unicorn/filename-case": ["error", {
        case: "camelCase",
        ignore: [
          // Ignore files ending with .md
          "^[^.]+\\.md$",
        ],
      }],
      // Configure unused-imports/no-unused-vars to ignore Schema variables
      "unused-imports/no-unused-vars": [
        "error",
        {
          vars: "all",
          varsIgnorePattern: "^.*Schema$", // Ignore variables ending with Schema
          args: "after-used",
          argsIgnorePattern: "^_",
        },
      ],
    },
  },
  // Package.json specific rules (as a separate object)
  {
    files: ["**/package.json"],
    rules: {
      // Sort package.json keys
      "jsonc/sort-keys": [
        "error",
        {
          pathPattern: "^$",
          order: [
            "name",
            "version",
            "private",
            "description",
            "author",
            "license",
            "type",
            "main",
            "types",
            "exports",
            "files",
            "bin",
            "scripts",
            "dependencies",
            "devDependencies",
            "peerDependencies",
            "optionalDependencies",
            "engines",
            "repository",
            "keywords",
            "homepage",
            "bugs",
          ],
        },
        {
          pathPattern: "^scripts$",
          order: { type: "asc" },
        },
        {
          pathPattern: "^(?:dev|peer|optional|)?[Dd]ependencies$",
          order: { type: "asc" },
        },
      ],
      // Enforce consistent spacing in package.json
      "jsonc/key-spacing": [
        "error",
        {
          beforeColon: false,
          afterColon: true,
          mode: "strict",
        },
      ],
    },
  },
);
