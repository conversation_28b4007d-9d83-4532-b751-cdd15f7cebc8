/**
 * Build Performance Monitor
 *
 * This script measures and compares build performance across different configurations
 * and provides insights for optimization.
 */

import { execSync } from "node:child_process";
import path from "node:path";
import { performance } from "node:perf_hooks";
import { fileURLToPath } from "node:url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, "..");

/**
 * Execute a command and measure its execution time
 */
function measureCommand(command, cwd = rootDir) {
  console.log(`\n🔄 Running: ${command}`);
  console.log(`📁 Working directory: ${cwd}`);

  const startTime = performance.now();

  try {
    const result = execSync(command, {
      cwd,
      stdio: "pipe",
      encoding: "utf8",
    });

    const endTime = performance.now();
    const duration = endTime - startTime;

    console.log(`✅ Completed in ${(duration / 1000).toFixed(2)}s`);

    return {
      success: true,
      duration,
      output: result,
    };
  } catch (error) {
    const endTime = performance.now();
    const duration = endTime - startTime;

    console.log(`❌ Failed in ${(duration / 1000).toFixed(2)}s`);
    console.error(error.message);

    return {
      success: false,
      duration,
      error: error.message,
    };
  }
}

/**
 * Clean build artifacts and caches
 */
function cleanAll() {
  console.log("\n🧹 Cleaning all build artifacts and caches...");

  const cleanCommands = [
    "cd service && pnpm run clean",
    "cd worker && pnpm run clean",
    "pnpm run clean:cache",
  ];

  for (const command of cleanCommands) {
    measureCommand(command);
  }
}

/**
 * Run build performance tests
 */
function runPerformanceTests() {
  console.log("\n🚀 Starting Build Performance Tests");
  console.log("=" .repeat(50));

  const results = {};

  // Test 1: Clean production build (no cache)
  console.log("\n📊 Test 1: Clean Production Build (No Cache)");
  cleanAll();
  results.cleanProdBuild = measureCommand("pnpm run build:parallel");

  // Test 2: Incremental production build (with cache)
  console.log("\n📊 Test 2: Incremental Production Build (With Cache)");
  results.incrementalProdBuild = measureCommand("pnpm run build:parallel");

  // Test 3: Clean development build (single build, not watch)
  console.log("\n📊 Test 3: Clean Development Build");
  cleanAll();
  results.cleanDevBuild = measureCommand("pnpm run build:dev");

  // Test 4: Service only build
  console.log("\n📊 Test 4: Service Only Build");
  cleanAll();
  results.serviceOnlyBuild = measureCommand("pnpm run build:prod", path.join(rootDir, "service"));

  // Test 5: Worker only build
  console.log("\n📊 Test 5: Worker Only Build");
  cleanAll();
  results.workerOnlyBuild = measureCommand("pnpm run build:prod", path.join(rootDir, "worker"));

  return results;
}

/**
 * Generate performance report
 */
function generateReport(results) {
  console.log("\n📈 Build Performance Report");
  console.log("=" .repeat(50));

  const formatTime = (ms) => `${(ms / 1000).toFixed(2)}s`;
  const formatStatus = (success) => success ? "✅" : "❌";

  console.log("\n🏗️  Build Times:");
  console.log(`  Clean Production Build:     ${formatStatus(results.cleanProdBuild.success)} ${formatTime(results.cleanProdBuild.duration)}`);
  console.log(`  Incremental Production:     ${formatStatus(results.incrementalProdBuild.success)} ${formatTime(results.incrementalProdBuild.duration)}`);
  console.log(`  Clean Development Build:    ${formatStatus(results.cleanDevBuild.success)} ${formatTime(results.cleanDevBuild.duration)}`);
  console.log(`  Service Only:               ${formatStatus(results.serviceOnlyBuild.success)} ${formatTime(results.serviceOnlyBuild.duration)}`);
  console.log(`  Worker Only:                ${formatStatus(results.workerOnlyBuild.success)} ${formatTime(results.workerOnlyBuild.duration)}`);

  // Calculate cache effectiveness
  if (results.cleanProdBuild.success && results.incrementalProdBuild.success) {
    const cacheImprovement = ((results.cleanProdBuild.duration - results.incrementalProdBuild.duration) / results.cleanProdBuild.duration) * 100;
    console.log(`\n💾 Cache Effectiveness: ${cacheImprovement.toFixed(1)}% improvement`);
  }

  // Calculate parallel build efficiency
  if (results.serviceOnlyBuild.success && results.workerOnlyBuild.success && results.cleanProdBuild.success) {
    const sequentialTime = results.serviceOnlyBuild.duration + results.workerOnlyBuild.duration;
    const parallelTime = results.cleanProdBuild.duration;
    const parallelEfficiency = ((sequentialTime - parallelTime) / sequentialTime) * 100;
    console.log(`⚡ Parallel Build Efficiency: ${parallelEfficiency.toFixed(1)}% time saved`);
  }

  console.log("\n💡 Optimization Recommendations:");

  if (results.cleanProdBuild.duration > 10000) {
    console.log("  • Consider enabling more aggressive caching");
    console.log("  • Review bundle size and dependencies");
  }

  if (results.incrementalProdBuild.duration > results.cleanProdBuild.duration * 0.5) {
    console.log("  • Cache effectiveness could be improved");
    console.log("  • Check TypeScript incremental compilation settings");
  }

  console.log("\n🎯 Next Steps:");
  console.log("  • Run this script regularly to track performance trends");
  console.log("  • Use 'pnpm run build:analyze' to analyze bundle sizes");
  console.log("  • Monitor build times in CI/CD pipeline");
}

/**
 * Main execution
 */
function main() {
  console.log("🔧 Build Performance Monitor");
  console.log("This tool will measure build performance and provide optimization insights.\n");

  try {
    const results = runPerformanceTests();
    generateReport(results);

    console.log("\n✨ Performance analysis complete!");
  } catch (error) {
    console.error("\n❌ Performance analysis failed:", error.message);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { generateReport, measureCommand, runPerformanceTests };
