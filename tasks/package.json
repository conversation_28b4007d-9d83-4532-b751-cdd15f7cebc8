{"name": "@95octane/tasks", "private": true, "description": "Tasks for 95octane", "license": "UNLICENSED", "type": "module", "scripts": {"getlatlng": "ts-node scripts/getlatlng.ts", "getroutes": "ts-node scripts/getroutes.ts", "import": "ts-node scripts/import.ts", "lint": "eslint .", "new-ride-id": "ts-node scripts/generateRideId.ts", "reset-rides": "ts-node scripts/reset-rides.ts"}, "dependencies": {"axios": "latest", "firebase-admin": "latest", "nanoid": "latest"}, "devDependencies": {"ts-node": "latest", "typescript": "latest"}, "workspaces": ["."]}