import type * as activities from "@/activities";
import { proxyActivities, sleep } from "@temporalio/workflow";

const { rideStatusUpdate } = proxyActivities<typeof activities>({
  retry: {
    backoffCoefficient: 2,
    initialInterval: "1 second",
    maximumAttempts: 5,
    maximumInterval: "5 seconds",
  },
  startToCloseTimeout: "1 minute",
});

export async function rideStatusWorkflow(
  rideId: string,
  rideTime: string,
  status: string,
): Promise<void> {
  // Calculate time until the ride starts/ends
  const rideDateTime = new Date(rideTime);
  const now = new Date();
  const timeUntilRide = rideDateTime.getTime() - now.getTime();

  if (timeUntilRide > 0) {
    // Sleep until the ride starts
    await sleep(timeUntilRide);
  }

  await rideStatusUpdate(rideId, status as activities.rideStatusEnum);
}
