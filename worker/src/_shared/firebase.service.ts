import type { DocumentData } from "firebase-admin/firestore";
import admin from "firebase-admin";
import { UserRecord } from "firebase-admin/auth";
import {

  FieldPath,
  FieldValue,
  Timestamp,
} from "firebase-admin/firestore";

export { type DocumentData, FieldPath, FieldValue, Timestamp, UserRecord };

export const setOptions = { merge: true };

export class FirebaseService {
  private static initialized: boolean = false;
  private static firestore: admin.firestore.Firestore;

  private static initialize() {
    if (this.initialized) {
      return;
    }

    if (admin.apps.length) {
      this.initialized = true;
      return;
    }

    admin.initializeApp();
    this.firestore = admin.firestore();
    this.firestore.settings({
      cacheSizeBytes: 0,
      ignoreUndefinedProperties: true,
    });

    this.initialized = true;
  }

  public static getAuth() {
    this.initialize();
    return admin.auth();
  }

  public static getFirestore() {
    this.initialize();
    return this.firestore;
  }

  public static getStorage() {
    this.initialize();
    return admin.storage();
  }

  public static getMessaging() {
    this.initialize();
    return admin.messaging();
  }
}

/**
 * Recursively converts all Date objects in an object to Firestore Timestamps
 * @param obj - The object to process
 * @returns A new object with all Date objects converted to Timestamps
 */
export function convertDatesToTimestamps<T>(obj: T): T {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (obj instanceof Date) {
    return Timestamp.fromDate(obj) as unknown as T;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => convertDatesToTimestamps(item)) as unknown as T;
  }

  if (typeof obj === "object") {
    const result: Record<string, unknown> = {};

    for (const [key, value] of Object.entries(obj)) {
      result[key] = convertDatesToTimestamps(value);
    }

    return result as T;
  }

  return obj;
}

/**
 * Recursively converts all Firestore Timestamps in an object to JavaScript Date objects
 * @param obj - The object to process
 * @returns A new object with all Timestamps converted to Date objects
 */
export function convertTimestampsToDates<T>(obj: T): T {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (obj instanceof Timestamp) {
    return obj.toDate() as unknown as T;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => convertTimestampsToDates(item)) as unknown as T;
  }

  if (typeof obj === "object") {
    const result: Record<string, unknown> = {};

    for (const [key, value] of Object.entries(obj)) {
      result[key] = convertTimestampsToDates(value);
    }

    return result as T;
  }

  return obj;
}
