import { fileURLToPath } from "node:url";
import { NativeConnection, Worker } from "@temporalio/worker";
import { config } from "@/_shared/config";
import * as activities from "@/activities";

async function run() {
  const temporalConnection = await NativeConnection.connect(
    config.TEMPORAL_CONNECTION,
  );
  const worker = await Worker.create({
    activities,
    connection: temporalConnection,
    namespace: config.NAMESPACE,
    taskQueue: config.TASK_QUEUE,
    workflowsPath: fileURLToPath(new URL("./workflows", import.meta.url)),
  });

  await worker.run();
}

run().catch((err) => {
  console.error(err);
  process.exit(1);
});
