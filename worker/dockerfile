FROM chainguard/node:latest AS base
WORKDIR /app

RUN npm update --verbose
COPY --chown=node:node package.json package-lock.json ./

# Install production dependencies
FROM base AS prod-deps
RUN npm ci --omit=dev --verbose

# Install build dependencies
FROM base AS build-deps
ARG RELEASE_TYPE=unknown
RUN npm ci --verbose
COPY --chown=node:node . .
RUN npm run build:${RELEASE_TYPE}

# Runtime
FROM base AS runtime
ARG RELEASE_TYPE=unknown
COPY --from=prod-deps /app/node_modules ./node_modules
COPY --from=build-deps /app/dist ./
COPY --from=base /app/package.json ./package.json

ENV HOST=0.0.0.0
ENV PORT=8080
ENV RUNTIME_ENV=${RELEASE_TYPE}

EXPOSE 8080
CMD ["--enable-source-maps", "./worker.js"]
