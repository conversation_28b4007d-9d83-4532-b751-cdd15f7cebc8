default_install_hook_types:
  - pre-commit
  - commit-msg
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: check-shebang-scripts-are-executable
      - id: check-merge-conflict
      - id: detect-private-key
      - id: end-of-file-fixer
      - id: forbid-submodules
      - id: trailing-whitespace
  - repo: local
    hooks:
      - id: local-npm-run-lint-service
        name: NPM Run Linter (service)
        entry: cd service && pnpm run lint:fix
        language: system
      # - id: local-npm-run-lint-worker
      #   name: N<PERSON> <PERSON> (worker)
      #   entry: cd worker && pnpm run lint:fix
      #   language: system
      # - id: local-npm-run-lint-test
      #   name: N<PERSON> (test)
      #   entry: cd test && pnpm run lint:fix
      #   language: system
  - repo: https://github.com/gitleaks/gitleaks
    rev: v8.27.2
    hooks:
      - id: gitleaks
        args: [--config, ./.gitleaks.toml]
  - repo: https://github.com/qoomon/git-conventional-commits
    rev: v2.7.2
    hooks:
      - id: conventional-commits
