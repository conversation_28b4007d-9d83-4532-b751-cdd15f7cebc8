---
convention:
  commitTypes:
    - feat
    - fix
    - perf
    - refactor
    - style
    - test
    - build
    - ci
    - ops
    - docs
    - chore
    - merge
    - revert
    - release
    - deps
    - security
    - config
    - wip
    - add
  commitScopes:
    - wip
  releaseTagGlobPattern: '^(?:web|android|ios)-v\d{0,3}\.\d{0,3}\.\d{0,3}(\+\d{1,3})?$'

changelog:
  commitTypes:
    - feat
    - fix
    - perf
    - merge
  includeInvalidCommits: true
  commitIgnoreRegexPattern: '^[wW][iI][pP]\b'
  headlines:
    feat: Features
    fix: Bug Fixes
    perf: Performance Improvements
    merge: Merges
    breakingChange: BREAKING CHANGES

  # GitHub
  commitUrl: https://gitlab.com/95octane/apiService/commit/%commit%
  commitRangeUrl: https://gitlab.com/95octane/apiService/compare/%from%...%to%?diff=split

  # GitHub Issues
  issueRegexPattern: "#[0-9]+"
  issueUrl: https://gitlab.com/95octane/apiService/issues/%issue%
