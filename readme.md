# 95octane Backend Monorepo

This is a monorepo for the 95octane backend services.

## 🏗️ Architecture

- **`service`**: Fastify API service
- **`worker`**: Temporal-based background worker
- **`tasks`**: Ad-hoc scripts for data management

## 🚀 Tech Stack

- [NodeJS](https://nodejs.org/) - Runtime environment
- [TypeScript](https://www.typescriptlang.org/) - Type-safe development
- [Fastify](https://www.fastify.io/) - Web framework
- [Zod](https://zod.dev/) - Schema validation and type inference
- [Firebase](https://firebase.google.com/) - Backend services (Firestore, Auth)
- [Temporal](https://temporal.io/) - Workflow orchestration
- [Webpack](https://webpack.js.org/) - Module bundling
- [Vitest](https://vitest.dev/) - Testing framework
- [PNPM](https://pnpm.io/) - Package manager

## 🛠️ Development

### Quick Start

```bash
# Install dependencies
pnpm install

# Start development server (service only)
just serve

# Start individual services
just service-dev  # API service only
just worker       # Background worker only

# Run tests
just test

# Run linting
just lint
```

## 🔧 Configuration

### Environment Variables

- `RUNTIME_ENV`: Environment (development/production/test/local) - default: production
- `LOG_LEVEL`: Logging level (silent/error/warn/info/debug) - default: warn
- `USE_EMULATOR`: Use Firebase emulator (true/false) - default: false
- `MINIMUM_APP_VERSION`: Minimum supported app version - default: 1
- `HOST`: Server host - default: 0.0.0.0
- `PORT`: Server port - default: 8888
- `HTTP2`: Enable HTTP/2 support - default: true
- `HTTPS`: Enable HTTPS - default: false
- `GOOGLE_APPLICATION_CREDENTIALS`: Path to Firebase service account key
- `SSL_CERT`: SSL certificate path (for HTTPS)
- `SSL_KEY`: SSL private key path (for HTTPS)

## 📚 Documentation

### Core Documentation

- **[Project Structure](./PROJECT_STRUCTURE.md)** - Detailed project structure and architecture
- **[Coding Standards](./CODING_STANDARDS.md)** - Code style and best practices
- **[API Documentation](./docs/service.md)** - API endpoints and usage

### Additional Resources

- **[Setup Guide](./docs/readme.md)** - Local development setup
- **[Testing Guide](./service/src/_testUtils/)** - Testing utilities and patterns
- **[Build Optimization](./docs/build-optimization.md)** - Performance optimization guide
