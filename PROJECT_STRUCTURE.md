# 95octane Backend Projects

This document outlines the structure for the 95octane backend projects.

## Monorepo Structure

This project is a monorepo and contains the following code repositories:

- `service`: Fastify API service
- `worker`: Temporal-based background worker
- `tasks`: Ad-hoc scripts for data management
- `docs`: Project documentation

## File Structure (`/service`)

```text
/service
├── src/
│   ├── main.ts               # Entry point
│   │
│   ├── _shared/              # Shared utilities
│   │   ├── common.schema.ts         # Common schemas and types
│   │   ├── config.ts                # Application configuration
│   │   ├── config.schema.ts         # Configuration validation
│   │   ├── error.ts                 # Error handling
│   │   ├── fastify.ts               # Fastify app builder
│   │   ├── firebase.ts              # Firebase integration
│   │   ├── httpClient.ts            # HTTP client utilities
│   │   ├── logger.ts                # Logging configuration
│   │   ├── nanoid.ts                # ID generation utilities
│   │   └── tryCatch.ts              # Error handling utilities
│   │
│   ├── _middleware/          # Middleware components
│   │   ├── auth.plugin.ts           # Authentication middleware
│   │   ├── custom.route.options.ts  # Custom route options
│   │   ├── custom.types.ts          # Custom type definitions
│   │   ├── error.handler.ts         # Error handling middleware
│   │   └── headers.plugin.ts        # Header validation middleware
│   │
│   ├── _testUtils/           # Testing utilities
│   │   ├── app.ts                   # Test app setup
│   │   ├── auth.ts                  # Authentication test helpers
│   │   ├── cleanup.ts               # Test cleanup utilities
│   │   ├── common.ts                # Common test utilities
│   │   ├── firebase.ts              # Firebase test helpers
│   │   └── data/                    # Test data fixtures
│   │
│   └── modules/              # Feature modules
│       ├── routes.ts                # Main route setup
│       │
│       ├── common/           # Common utilities
│       │   ├── config.model.ts      # Configuration models
│       │   ├── suntimes.model.ts    # Sun times calculations
│       │   └── utils.ts             # Common utility functions
│       │
│       ├── init/             # Initialization module
│       │   ├── init.api.ts          # Initialization API handlers
│       │   ├── init.routes.ts       # Initialization routes
│       │   └── init.schema.ts       # Initialization schemas
│       │
│       ├── user/             # User management module
│       │   ├── user.api.ts          # User API handlers
│       │   ├── user.repo.ts         # User data access
│       │   ├── user.routes.ts       # User routes
│       │   ├── user.schema.ts       # User validation schemas
│       │   ├── user.service.ts      # User business logic
│       │   └── user.test.ts         # User tests
│       │
│       ├── ride/             # Ride management module
│       │   ├── ride.repo.ts         # Ride data access
│       │   ├── ride.routes.ts       # Ride routes
│       │   ├── ride.schema.ts       # Ride validation schemas
│       │   ├── ride.service.ts      # Ride business logic
│       │   ├── ride.test.ts         # Ride tests
│       │   └── rides.api.ts         # Ride API handlers
│       │
│       ├── routes/           # Route management module
│       │   ├── route.api.ts         # Route API handlers
│       │   ├── route.routes.ts      # Route routes
│       │   ├── route.schema.ts      # Route validation schemas
│       │   └── route.test.ts        # Route tests
│       │
│       └── places/           # Places/location module
│           ├── place.api.ts         # Places API handlers
│           ├── place.repo.ts        # Places data access
│           ├── place.routes.ts      # Places routes
│           ├── place.schema.ts      # Places validation schemas
│           └── place.service.ts     # Places business logic
│
├── package.json                     # Package configuration
├── tsconfig.json                    # TypeScript configuration
├── tsconfig.eslint.json             # ESLint TypeScript configuration
├── vitest.config.ts                 # Vitest testing configuration
├── waitOn.json                      # Service dependency waiting configuration
├── webpack.config.mjs               # Webpack configuration
└── dockerfile                       # Container definition
```

### Module Structure Pattern

Each functional module follows this pattern:

1. **API Layer** (`*.api.ts`):
   - Implements API handlers/controllers
   - Coordinates between services and repositories
   - Returns structured responses
   - Handles request/response logic

2. **Routes Layer** (`*.routes.ts`):
   - Defines HTTP routes
   - Connects routes to API handlers
   - Applies request validation using Zod schemas
   - Includes authentication and authorization checks

3. **Schema Layer** (`*.schema.ts`):
   - Defines Zod validation schemas
   - Exports TypeScript type definitions
   - Ensures runtime type safety and validation
   - Supports request/response schema validation

4. **Service Layer** (`*.service.ts`):
   - Implements business logic
   - Handles complex operations and data transformations
   - Coordinates between repositories
   - Contains domain-specific logic

5. **Repository Layer** (`*.repo.ts`):
   - Abstracts data access
   - Interacts with Firestore
   - Formats data for service layer
   - Handles database operations

6. **Test Layer** (`*.test.ts`):
   - Unit and integration tests
   - Uses Vitest for test execution
   - Includes test utilities and fixtures
   - Covers API endpoints, business logic, and data access

## File Structure (`/worker`)

```text
/worker
├── src/
│   ├── main.ts               # Entry point with Temporal worker setup
│   │
│   ├── _shared/              # Shared utilities
│   │   ├── config.ts                # Application configuration
│   │   └── firebase.service.ts      # Firebase service integration
│   │
│   ├── activities/           # Temporal activities
│   │   ├── index.ts                 # Export of all activities
│   │   │
│   │   └── rides/            # Ride-related activities
│   │       ├── rideStatusUpdate.ts  # Ride status update activity
│   │       └── ride.schema.ts       # Activity validation schemas (Zod)
│   │
│   └── workflows/            # Temporal workflows
│       ├── index.ts                 # Workflow exports (required by Temporal)
│       └── rideStatus.ts            # Ride status workflow
│
├── package.json                     # Package configuration
├── tsconfig.json                    # TypeScript configuration
├── tsconfig.eslint.json             # ESLint TypeScript configuration
├── nodemon.json                     # Nodemon configuration for development
├── webpack.config.mjs               # Webpack configuration
└── dockerfile                       # Container definition
```

### Workflow Structure Pattern

Each workflow follows this pattern:

1. **Workflow Definition** (`workflows/*.ts`):
   - Defines the workflow logic
   - Uses proxyActivities to call activities
   - Implements error handling and retry logic
   - Manages workflow state and timing

2. **Activity Definition** (`activities/**/*.ts`):
   - Defines one or more activity functions
   - Exports the activity functions and their schemas
   - Handles external service calls
   - Contains business logic for specific tasks

3. **Schema Definition** (`**/*.schema.ts`):
   - Defines the input and output schemas using Zod
   - Exports TypeScript type definitions
   - Ensures runtime type safety and validation
   - Supports data validation

## File Structure (`/tasks`)

```text
/tasks
├── scripts/                  # Task scripts
├── testData/                 # Test data for tasks
├── package.json              # Package configuration
└── tsconfig.json             # TypeScript configuration
```

## Additional Files

```text
/
├── .just/                    # Just command definitions
│   ├── common.just                  # Common commands
│   ├── root.just                    # Root-level commands
│   ├── service.just                 # Service-specific commands
│   ├── worker.just                  # Worker-specific commands
│   ├── tasks.just                   # Task-specific commands
│   └── release.just                 # Release commands
│
├── docs/                     # Documentation
│   ├── readme.md                    # Setup documentation
│   ├── service.md                   # API documentation
│   ├── tasks.md                     # Tasks documentation
│   ├── build-optimization.md        # Build optimization guide
│   ├── gcp-service-account.md       # GCP setup guide
│   ├── http2-setup.md               # HTTP/2 configuration
│   └── rides/                       # Ride-specific documentation
│
├── scripts/                  # Build and utility scripts
├── justfile                  # Just command runner configuration
├── package.json              # Root package configuration
├── pnpm-lock.yaml           # PNPM lock file
├── pnpm-workspace.yaml      # PNPM workspace configuration
├── eslint.config.mjs        # ESLint configuration
├── firebase.json            # Firebase configuration
├── firestore.indexes.json   # Firestore indexes
├── firestore.rules          # Firestore security rules
└── storage.rules            # Firebase Storage rules
```
