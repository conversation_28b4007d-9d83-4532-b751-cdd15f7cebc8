# HTTP/2 Configuration

This document explains how HTTP/2 is configured in the 95octane API service.

## Overview

The service now supports HTTP/2 with the following configurations:

- **Production**: HTTP/2 over HTTPS (handled by Google Cloud Run)
- **Development**: HTTP/2 over cleartext (h2c) - works well with reverse proxies
- **Local HTTPS**: HTTP/2 over HTTPS with custom certificates (optional)

## Environment Variables

### HTTP2

- **Default**: `true`
- **Description**: Enables HTTP/2 support
- **Example**: `export HTTP2="true"`

### HTTPS

- **Default**: `false`
- **Description**: Enables HTTPS mode (requires SSL certificates)
- **Example**: `export HTTPS="true"`

### SSL_KEY

- **Default**: `undefined`
- **Description**: Path to SSL private key file (required when HTTPS=true)
- **Example**: `export SSL_KEY="/path/to/private.key"`

### SSL_CERT

- **Default**: `undefined`
- **Description**: Path to SSL certificate file (required when HTTPS=true)
- **Example**: `export SSL_CERT="/path/to/certificate.crt"`

## Configuration Modes

### 1. HTTP/2 over Cleartext (h2c) - Development Default

```bash
export HTTP2="true"
export HTTPS="false"
```

This is the default development configuration. It enables HTTP/2 without requiring SSL certificates, which works well when:

- Running behind a reverse proxy (nginx, Cloud Run, etc.)
- Testing HTTP/2 features in development
- Deploying to platforms that handle SSL termination

### 2. HTTP/2 over HTTPS - Production/Secure Development

```bash
export HTTP2="true"
export HTTPS="true"
export SSL_KEY="/path/to/private.key"
export SSL_CERT="/path/to/certificate.crt"
```

This configuration enables full HTTP/2 over HTTPS with your own certificates.

### 3. HTTP/1.1 - Fallback

```bash
export HTTP2="false"
```

Disables HTTP/2 and falls back to HTTP/1.1.

## Google Cloud Run Deployment

Google Cloud Run automatically handles:

- SSL certificate provisioning and renewal
- HTTP/2 termination
- Load balancing

The service is configured to use HTTP/2 over cleartext internally, while Cloud Run provides the HTTPS termination for external clients.

## Testing HTTP/2

You can test if HTTP/2 is working using curl:

```bash
# Test HTTP/2 support
curl -I --http2 https://your-service-url.com/healthcheck

# Check the protocol version in the response headers
# Look for "HTTP/2" in the status line
```

Or using browser developer tools:

1. Open Network tab
2. Make a request to your service
3. Check the "Protocol" column (should show "h2" for HTTP/2)

## Troubleshooting

### SSL Certificate Issues

If you see warnings about SSL certificates in development:

- The service will automatically fall back to HTTP/1.1
- Check that SSL_KEY and SSL_CERT paths are correct
- Ensure certificate files are readable by the application

### HTTP/2 Not Working

1. Verify HTTP2 environment variable is set to "true"
2. Check server logs for HTTP/2 initialization messages
3. Ensure client supports HTTP/2
4. For production, verify Cloud Run is configured correctly

## Performance Benefits

HTTP/2 provides several advantages:

- **Multiplexing**: Multiple requests over a single connection
- **Header Compression**: Reduced overhead with HPACK
- **Server Push**: Proactive resource delivery (if implemented)
- **Binary Protocol**: More efficient than HTTP/1.1 text protocol

These improvements are particularly beneficial for mobile clients and high-traffic scenarios.
