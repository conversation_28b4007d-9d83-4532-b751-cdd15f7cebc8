# Service (API)

This document describes all the API endpoints available in the 95octane API service.

## Health Check

```http
GET /healthcheck
```

> `Auth`: false

Returns a simple health check response to verify the service is running.

**Response:**
```json
{
  "success": true
}
```

## Initialization

### Get Initial Settings

```http
POST /init
```

> `Auth`: false

This API is used to get the initial settings. Returns `userEnabled` boolean indicating whether test users can login (enabled in development/test environments).

**Response:**
```json
{
  "userEnabled": true
}
```

## User Management

### Get User Details

```http
POST /user/:userId
```

> `Auth`: true

This API is used to get the user details along with client configuration and location-based data.

**Request Body:**
```json
{
  "lat": 37.7749,
  "lng": -122.4194,
  "notificationToken": "string"
}
```

**Response:**
```json
{
  "user": {
    "id": "string",
    "email": "string",
    "name": "string",
    "phoneNumber": "string",
    "photoURL": "string",
    "isEmailVerified": true,
    "provider": ["firebase"],
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  },
  "config": {
    "config": {
      "revenueCatAPIKey": "string",
      "urlPrivacy": "string",
      "urlTerms": "string",
      "urlAboutUs": "string"
    },
    "sunrise": "06:30",
    "sunset": "18:45"
  }
}
```

### Save User Settings

```http
POST /user/:userId/settings
```

> `Auth`: true

Save user preferences and settings.

### Save Notification Token

```http
POST /user/:userId/notification-token
```

> `Auth`: true

Save the user's FCM notification token for push notifications.

### Save Alternative Details

```http
POST /user/:userId/alt
```

> `Auth`: true

Save alternative user details (backup contact information, etc.).

## Ride Management

### Get Rides by User

```http
GET /rides/:userId
```

> `Auth`: true

Get all rides for a specific user.

**Response:**
```json
{
  "rides": [
    {
      "id": "string",
      "userId": "string",
      "status": "active",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

### Get Single Ride

```http
GET /ride/:rideId
```

> `Auth`: true

Get details of a specific ride.

### Create Ride

```http
POST /ride
```

> `Auth`: true

Create a new ride.

### Update Ride

```http
POST /ride/:rideId
```

> `Auth`: true

Update an existing ride.

### RSVP to Ride

```http
POST /ride/:rideId/rsvp
```

> `Auth`: true

RSVP to a ride as a participant.

### Share Location

```http
POST /ride/:rideId/location
```

> `Auth`: true

Share current location for a ride.

## Route Management

### Get Route

```http
GET /route/:rideId/:direction
```

> `Auth`: true

Get route information for a ride in a specific direction (to/from).

**Parameters:**
- `rideId`: The ride identifier
- `direction`: Either "to" or "from"

## Places

### Search Places

```http
GET /places/search
```

> `Auth`: true

Search for places using Google Places API autocomplete.

**Query Parameters:**
- `query`: Search query string
- `lat`: Latitude for location bias
- `lng`: Longitude for location bias

**Response:**
```json
{
  "places": [
    {
      "placeId": "string",
      "description": "string",
      "mainText": "string",
      "secondaryText": "string"
    }
  ]
}
```

- `lat` - Latitude of the user
- `lng` - Longitude of the user
- `platform` - Platform of the user (ios or android)
- `user` - User details (as returned by Firebase Auth's User object)
