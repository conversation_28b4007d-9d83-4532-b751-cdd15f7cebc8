# Service Pipeline for CI/CD (dev/prod)

include:
  - local: .gitlab/pipelines/variables.yaml

stages:
  - build
  - deploy
  - firebase

build:
  stage: build
  image: ${BUILDTOOLS_IMAGE}
  timeout: 15 minutes
  services:
    - docker:dind
  before_script:
    # Authenticate using service account key
    - gcloud auth activate-service-account --key-file=${GOOGLE_APPLICATION_CREDENTIALS}
    - gcloud config set project ${GCP_PROJECT_ID}
    # Configure Docker to use gcloud as credential helper
    - gcloud auth configure-docker $ARTIFACT_REGISTRY
  script:
    - ./.tools/build-image service

deploy:
  stage: deploy
  image: ${BUILDTOOLS_IMAGE}
  timeout: 15 minutes
  before_script:
    # Authenticate using service account key
    - gcloud auth activate-service-account --key-file=${GOOGLE_APPLICATION_CREDENTIALS}
    - gcloud config set project ${GCP_PROJECT_ID}
  script:
    - ./.tools/deploy service

firebase:
  stage: firebase
  image: ${BUILDTOOLS_IMAGE}
  timeout: 15 minutes
  before_script:
    # Authenticate using service account key
    - gcloud auth activate-service-account --key-file=${GOOGLE_APPLICATION_CREDENTIALS}
    - gcloud config set project ${GCP_PROJECT_ID}
    # Authenticate Firebase
    - firebase use prod-95octane-app
  script:
    # Deploy firestore rules & storage rules
    - firebase deploy --only firestore:rules,storage
