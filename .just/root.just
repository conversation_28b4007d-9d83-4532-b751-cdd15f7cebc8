[group('backend')]
[doc('Run the development server for service & worker')]
@serve:
  echo -e "{{GREEN + BOLD}}Running development server for service...{{NORMAL}}"
  just service-serve

[group('backend')]
[doc('Run Lint for all targets')]
@lint:
  echo -e "{{GREEN + BOLD}}Linting root ...{{NORMAL}}"
  -npx eslint --color --fix package.json
  echo -e "{{GREEN + BOLD}}Linting service ...{{NORMAL}}"
  -npx eslint --color --fix service/
  echo -e "{{GREEN + BOLD}}Linting worker ...{{NORMAL}}"
  -npx eslint --color --fix worker/

[group('backend')]
[doc('Run service development server')]
@dev: service-dev

[group('backend')]
[doc('Run service tests')]
@test: service-test

[group('backend')]
[doc('Run service tests in watch mode')]
@test-watch: service-test-watch

[group('backend')]
[doc('Run service test coverage')]
@coverage: service-coverage
