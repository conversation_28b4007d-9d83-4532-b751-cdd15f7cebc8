#! /usr/bin/env bash

set -e

SCRIPT_DIR=$(dirname "$0")

# Order of inclusion is important
source ${SCRIPT_DIR}/utils/colors.env
source ${SCRIPT_DIR}/utils/args.env
source ${SCRIPT_DIR}/utils/checks.bash

# Should always be included last
source ${SCRIPT_DIR}/utils/common.bash

# Release the image in Google Cloud Run
echo -e "${COLOR_GREEN}Deploying ${IMAGE_NAME}:${IMAGE_TAG} in Google Cloud Run...${COLOR_RESET}"
gcloud run services update ${RELEASE_TYPE}-${APP_NAME} \
  --image "${IMAGE_PREFIX}/${IMAGE_NAME}:${IMAGE_TAG}" \
  --region ${DEPLOYMENT_REGION} \
  --min-instances=0 \
  --max-instances=5 \
  --no-use-http2 \
  --concurrency=1000 \
  --cpu-boost \
  --cpu-throttling \
  --execution-environment=gen2 \
  --ingress=all \
  --revision-suffix=${IMAGE_TAG} \
  --tag=${IMAGE_NAME} \
  --labels="app=${IMAGE_NAME}" \
  --timeout=30s \
  --cpu=2 --gpu=0 --memory=2Gi --port=8888 \
  --liveness-probe="initialDelaySeconds=10,timeoutSeconds=2,httpGet.port=8888,httpGet.path=/healthcheck" \
  --startup-probe="timeoutSeconds=2,httpGet.port=8888,httpGet.path=/healthcheck" \
  --env-vars-file=${SCRIPT_DIR}/utils/${RELEASE_TYPE}-${APP_NAME}.yaml
echo -e "${COLOR_GREEN}Done!${COLOR_RESET}"
