# Static variables for all apps
APP_NAME=${APP_NAME}
SERVICE_APP_NAME="service"
WORKER_APP_NAME="worker"
IMAGE_PREFIX="asia-southeast1-docker.pkg.dev/prod-95octane-app/docker-sg"
DEPLOYMENT_REGION="asia-southeast1"

# Get the release type from DEPLOY_ENV variable or git branch
if [[ -n ${DEPLOY_ENV} ]]; then
  # Running in CI/CD pipeline with DEPLOY_ENV set
  RELEASE_TYPE=${DEPLOY_ENV}
else
  # Running locally, detect from git branch
  BRANCH=$(git rev-parse --abbrev-ref HEAD 2>/dev/null)
  if [[ $? -ne 0 ]]; then
    echo -e "${COLOR_RED}ERROR:${COLOR_RESET}"
    echo -e "  ${COLOR_YELLOW}Unable to determine git branch. Make sure you're in a git repository.${COLOR_RESET}"
    exit 1
  fi
  if [[ ${BRANCH} == "develop" ]]; then
    RELEASE_TYPE="dev"
  elif [[ ${BRANCH} == "main" ]]; then
    RELEASE_TYPE="prod"
  else
    echo -e "${COLOR_RED}ERROR:${COLOR_RESET}"
    echo -e "  ${COLOR_YELLOW}Branch name '${BRANCH}' is not allowed, please switch to 'develop' or 'main' branch and try again.${COLOR_RESET}"
    exit 1
  fi
fi

# Get build number from package.json
BUILD_NO=$(grep '"version":' ./${APP_NAME}/package.json | head -n 1 | sed 's/"version": //' | sed 's/[\",[:blank:]]//g' | sed 's/.*+//')
if ! check_build_no_format ${BUILD_NO}; then
  echo -e "${COLOR_RED}ERROR${COLOR_RESET}"
  echo -e "${COLOR_YELLOW}BUILD_NO in package.json '${BUILD_NO}' should be numeric, please update it and try again.${COLOR_RESET}"
  exit 1
fi

# Set image name & tag
IMAGE_NAME="${RELEASE_TYPE}-${APP_NAME}"
IMAGE_TAG="v${BUILD_NO}"
