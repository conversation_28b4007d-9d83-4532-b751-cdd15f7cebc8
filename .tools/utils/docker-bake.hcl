variable "APP_NAME" {
  default = ""
}

variable "IMAGE_PREFIX" {
  default = ""
}

variable "IMAGE_NAME" {
  default = ""
}

variable "IMAGE_TAG" {
  default = ""
}

variable "RELEASE_TYPE" {
  default = ""
}

# Base target for shared configuration
target "docker-base" {
  platforms = ["linux/amd64"]
  pull = true
  dockerfile = "${APP_NAME}/dockerfile"
  args = {
    RELEASE_TYPE = RELEASE_TYPE
  }
}

# Build target
target "default" {
  inherits = ["docker-base"]
  context = "./"
  tags = ["${IMAGE_PREFIX}/${IMAGE_NAME}:${IMAGE_TAG}"]
}
