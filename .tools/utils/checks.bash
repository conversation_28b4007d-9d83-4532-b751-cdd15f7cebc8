# Function to check if any uncommitted changes exist
function has_uncommitted_changes() {
  if [[ $(git status --porcelain) ]]; then
    return 0
  else
    return 1
  fi
}

# Function to check if any untracked files exist
function has_untracked_files() {
  if [[ $(git ls-files --others --exclude-standard) ]]; then
    return 0
  else
    return 1
  fi
}

# Function to check if any unpushed commits exist
function has_unpushed_commits() {
  if [[ $(git log --branches --not --remotes) ]]; then
    return 0
  else
    return 1
  fi
}

# Function to check if tag exists
function check_tag_exists() {
  if git tag --list $1 >/dev/null 2>&1; then
    return 1
  fi
  return 0
}

# Function to test if the version follows N.N.N+N format
function check_version_format() {
  if [[ $1 =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\+[0-9]{1,3}$ ]]; then
    return 0
  else
    return 1
  fi
}

# Function to test if the build_no is a valid number
function check_build_no_format() {
  if [[ $1 =~ ^[0-9]{1,4}$ ]]; then
    return 0
  else
    return 1
  fi
}
