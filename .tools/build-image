#! /usr/bin/env bash

set -e

SCRIPT_DIR=$(dirname "$0")

# Order of inclusion is important
source ${SCRIPT_DIR}/utils/colors.env
source ${SCRIPT_DIR}/utils/args.env
source ${SCRIPT_DIR}/utils/checks.bash

# Should always be included last
source ${SCRIPT_DIR}/utils/common.bash

# Build & push the image using docker buildx bake
echo -e "${COLOR_GREEN}Building image ${IMAGE_NAME}:${IMAGE_TAG} ...${COLOR_RESET}"
APP_NAME=${APP_NAME} IMAGE_PREFIX=${IMAGE_PREFIX} IMAGE_NAME=${IMAGE_NAME} IMAGE_TAG=${IMAGE_TAG} RELEASE_TYPE=${RELEASE_TYPE} docker buildx bake --file ${SCRIPT_DIR}/utils/docker-bake.hcl --push
