#! /usr/bin/env bash

set -e

SCRIPT_DIR=$(dirname "$0")
source ${SCRIPT_DIR}/utils/colors.env
source ${SCRIPT_DIR}/utils/args.env
source ${SCRIPT_DIR}/utils/config.env
source ${SCRIPT_DIR}/utils/git.bash
source ${SCRIPT_DIR}/utils/version.bash

# Generate the tag for the release
echo -en "${COLOR_GREEN}Generating tag for ${APP_NAME} release ... ${COLOR_RESET}"
get_release_type
get_build_no
TAG=${RELEASE_TYPE}-${APP_NAME}-v${BUILD_NO}
echo -e "${COLOR_GREEN}${TAG}${COLOR_RESET}"

# Check if the latest commit has tag
echo -en "Checking for duplicate release ... "
if git describe --exact-match --tags HEAD >/dev/null 2>&1 | egrep "^${TAG}"; then
  echo -e "${COLOR_RED}ERROR${COLOR_RESET}"
  echo -e "${COLOR_YELLOW}Latest commit already has a tag, please commit your changes and try again.${COLOR_RESET}"
  exit 1
fi
echo -e "${COLOR_YELLOW}OK${COLOR_RESET}"

# Check if tag exists already
echo -en "Checking tag ${TAG} ... "
if check_tag_exists ${TAG}; then
  echo -e "${COLOR_RED}ERROR${COLOR_RESET}"
  echo -e "${COLOR_YELLOW}Tag ${TAG} already exists, please update the version in package.json and try again.${COLOR_RESET}"
  exit 1
fi
echo -e "${COLOR_YELLOW}OK${COLOR_RESET}"

# Check if any untracked files exist
echo -en "Checking if any untracked files exist ... "
if has_untracked_files; then
  echo -e "${COLOR_RED}ERROR${COLOR_RESET}"
  echo -e "${COLOR_YELLOW}Untracked files exist, please commit or remove them and try again.${COLOR_RESET}"
  exit 1
fi
echo -e "${COLOR_YELLOW}OK${COLOR_RESET}"

# Check if any uncommitted changes exist
echo -en "Checking if any uncommitted changes exist ... "
if has_uncommitted_changes; then
  echo -e "${COLOR_RED}ERROR${COLOR_RESET}"
  echo -e "${COLOR_YELLOW}Uncommitted changes exist, please commit or remove them and try again.${COLOR_RESET}"
  exit 1
fi
echo -e "${COLOR_YELLOW}OK${COLOR_RESET}"

# Check if any unpushed commits exist
echo -en "Checking if any unpushed commits exist ... "
if has_unpushed_commits; then
  echo -e "${COLOR_RED}ERROR${COLOR_RESET}"
  echo -e "${COLOR_YELLOW}Unpushed commits exist, please push them and try again.${COLOR_RESET}"
  exit 1
fi
echo -e "${COLOR_YELLOW}OK${COLOR_RESET}"

# Tag the release
echo -en "Tagging ${TAG} ... ${COLOR_RED}"
if ! git tag --sign --message "Release ${TAG}" ${TAG}; then
  echo -e "${COLOR_RESET}"
  exit 1
fi
echo -e "${COLOR_YELLOW}OK${COLOR_RESET}"

# Push the tag to remote
echo -en "Pushing tag to remote ... "
if ! git push origin ${TAG}; then
  echo -e "${COLOR_RED}FAIL!!!${COLOR_RESET}"
  exit 1
fi
echo -e "${COLOR_YELLOW}Done!${COLOR_RESET}"

exit 0
