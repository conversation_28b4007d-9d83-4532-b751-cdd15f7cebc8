{
  "[javascript]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "[json]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "[jsonc]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "[markdown]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "[typescript]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "diffEditor.ignoreTrimWhitespace": false,
  "diffEditor.renderSideBySide": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "never",
  },
  "editor.formatOnSave": false,
  "editor.quickSuggestions": {
    "comments": true,
    "other": true,
    "strings": true
  },
  "editor.suggest.insertMode": "replace",
  "eslint.enable": true,
  "eslint.format.enable": true,
  "eslint.lintTask.enable": true,
  "eslint.lintTask.options": "-c eslint.config.mjs .",
  "eslint.options": {
    "extensions": [
      ".js",
      ".jsx",
      ".ts",
      ".tsx",
      ".vue",
      ".html",
      ".md"
    ]
  },
  // Silent the stylistic rules in you IDE, but still auto fix them
  "eslint.rules.customizations": [
    { "rule": "style/*", "severity": "off", "fixable": true },
    { "rule": "format/*", "severity": "off", "fixable": true },
    { "rule": "*-indent", "severity": "off", "fixable": true },
    { "rule": "*-spacing", "severity": "off", "fixable": true },
    { "rule": "*-spaces", "severity": "off", "fixable": true },
    { "rule": "*-order", "severity": "off", "fixable": true },
    { "rule": "*-dangle", "severity": "off", "fixable": true },
    { "rule": "*-newline", "severity": "off", "fixable": true },
    { "rule": "*quotes", "severity": "off", "fixable": true },
    { "rule": "*semi", "severity": "off", "fixable": true }
  ],
  // Enable eslint for all supported languages
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "vue",
    "html",
    "markdown",
    "json",
    "jsonc",
    "yaml",
    "toml",
    "xml",
    "gql",
    "graphql",
    "astro",
    "svelte",
    "css",
    "less",
    "scss",
    "pcss",
    "postcss",
  ],
  "eslint.run": "onSave",
  "eslint.useFlatConfig": true,
  "eslint.workingDirectories": [
    {
      "mode": "auto"
    }
  ],
  "explorer.fileNesting.enabled": true,
  "explorer.fileNesting.expand": false,
  "explorer.fileNesting.patterns": {
    ".gitignore": ".gitkeep,.pre-commit-config.yaml,analysis_options.yaml,git-conventional-commits.yaml,.gitleaks.toml,.gitleaksignore",
    "dockerfile": ".dockerignore,dockerfile.dev",
    "firebase.json": "firebase-debug.json,.firebaserc,*.rules,firestore.indexes.json",
    "package.json": "package-lock.json,eslint.config.js,.eslintrc.js,tsconfig.json,tsconfig.dev.json,eslint.config.mjs,.prettierrc.yaml,.firebaserc,webpack.config.mjs,nodemon.json,waitOn.json,jest.config.mjs,wrangler.jsonc,worker-configuration.d.ts,pnpm-lock.yaml,pnpm-workspace.yaml,vitest.config.ts,vite.config.ts,",
    ".envrc": ".env,.env.*,.nvmrc,.env.*.secrets",
    "readme.md": ".augment-guidelines,claude.md,.cursorrules,CODING_STANDARDS.md,PROJECT_STRUCTURE.md"
  },
  "files.associations": {
    "package.json": "jsonc",
    "*.env": "shellscript",
    ".env": "shellscript",
    ".env.*": "shellscript",
    ".envrc": "shellscript",
    "env.prod": "shellscript",
    "env.dev": "shellscript",
    "firebase.json": "jsonc",
  },
  "files.autoGuessEncoding": true,
  "files.exclude": {
    "**/*.log": true,
    "**/dist/": true,
    "**/node_modules/": true,
    "**/ui-debug.log": true
  },
  "outline.showKeys": true,
  "prettier.enable": false,
  "todo-tree.general.tags": [
    "TODO",
    "FIXME",
    "HACK",
    "REVIEW",
    "NOTE",
    "REFACTOR",
    "REMOVE"
  ],
  "todo-tree.highlights.defaultHighlight": {
    "hideFromActivityBar": true,
    "hideFromStatusBar": false,
    "type": "text-and-comment"
  }
}
