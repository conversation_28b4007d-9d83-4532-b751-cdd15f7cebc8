import { resolve } from "path";
import { defineConfig } from "vitest/config";

export default defineConfig({
  clearScreen: true,
  logLevel: "info",
  resolve: {
    alias: {
      "@": resolve(__dirname, "src"),
    },
  },
  test: {
    coverage: {
      ignoreEmptyLines: true,
      include: [
        "src/modules/**/*.ts",
      ],
      exclude: [
        "src/modules/common/*.ts",
      ],
      reporter: ["text", "json"],
    },
    env: {
      LOG_LEVEL: "info",
      RUNTIME_ENV: "test",
    },
    environment: "node",
    include: [
      "**/*.test.ts",
    ],
    onConsoleLog: (log, type) => {
      if (type === "stderr") {
        console.error(log);
      }
      console.info(log);
    },
    // testTimeout: 10000,
    watch: false,
  },
});
