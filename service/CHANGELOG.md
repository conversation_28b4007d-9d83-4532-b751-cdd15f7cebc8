# Changelog

All notable changes to the 95octane API Service will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added [unreleasaed]

- Proprietary license implementation

## [0.0.1+10] - 2024-12-19

### Added [0.0.1+10]

- LICENSE details and proprietary licensing
- GitLab CI/CD configuration
- HTTP/2 support for improved performance
- Client timezone data handling
- Docker image optimization
- Build performance improvements
- Vitest testing framework integration
- PNPM package manager support
- Code line counter utility

### Changed

- Disabled headers check for healthcheck API endpoint
- Refactored build and deployment process
- Updated to Zod v4 for schema validation
- Improved Docker image optimization
- Enhanced build performance with webpack optimizations
- Upgraded all dependencies to latest versions
- Migrated from Jest to Vitest for testing

### Fixed

- Linting issues resolved
- PNPM version selection corrected
- Suntimes API error handling (returns null on failure)
- Docker support for PNPM package manager

### Security

- Added Fastify Helmet for security headers
- Implemented CORS configuration
- Added authentication middleware

## [0.0.1] - Initial Release

### Added [0.0.1]

- **Core API Framework**
  - Fastify web framework with TypeScript
  - Zod schema validation and type safety
  - Pino structured logging
  - Firebase Admin SDK integration
  - HTTP status codes standardization

- **API Modules**
  - **Init Module**: Application initialization and health checks
  - **User Module**: User management and authentication
  - **Ride Module**: Ride booking and management system
  - **Routes Module**: Route planning and optimization
  - **Places Module**: Location and place management
  - **Common Module**: Shared utilities and configurations

- **Middleware & Plugins**
  - Authentication plugin with Firebase integration
  - Custom route options and types
  - Error handling middleware
  - Headers plugin for security and CORS
  - Custom Fastify type providers

- **Development Tools**
  - Webpack bundling with development/production configs
  - TypeScript configuration with path mapping
  - ESLint configuration for code quality
  - Test utilities and helpers
  - Development server with hot reload

- **Infrastructure**
  - Docker containerization
  - Firebase Firestore integration
  - Environment-based configuration
  - Structured error handling
  - HTTP client utilities with Axios

- **Testing Framework**
  - Comprehensive test setup with Vitest
  - Test utilities for authentication and data cleanup
  - Coverage reporting with V8
  - Mock data generation with Faker.js

### Technical Stack

- **Runtime**: Node.js with ES modules
- **Framework**: Fastify v4
- **Language**: TypeScript
- **Database**: Firebase Firestore
- **Validation**: Zod v4
- **Testing**: Vitest with V8 coverage
- **Bundling**: Webpack 5
- **Package Manager**: PNPM
- **Containerization**: Docker

---

## Release Notes

### Version Numbering

This project uses a custom versioning scheme: `MAJOR.MINOR.PATCH+BUILD`

- The `+BUILD` suffix indicates internal build iterations
- Current version `0.0.1+10` represents the 10th build of version 0.0.1

### Deployment

- Automated CI/CD pipeline with GitLab
- Docker-based deployment
- Environment-specific configurations
- Health check endpoints for monitoring

### API Documentation

For detailed API documentation, refer to the individual module schema files:

- `/src/modules/init/init.schema.ts`
- `/src/modules/user/user.schema.ts`
- `/src/modules/ride/ride.schema.ts`
- `/src/modules/routes/route.schema.ts`
- `/src/modules/places/place.schema.ts`
