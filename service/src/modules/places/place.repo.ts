import type { PlacesSearchRequest, PlacesSearchResponse } from "./place.schema";

export class PlacesRepo {
  static async search(input: PlacesSearchRequest): Promise<PlacesSearchResponse> {
    const response = await fetch(
      `https://maps.googleapis.com/maps/api/place/findplacefromtext/output?parameters=${input}`,
    );

    if (!response.ok) {
      throw new Error(`Error fetching places: ${response.statusText}`);
    }

    const data: PlacesSearchResponse = await response.json();
    return data;
  }
}
