import { z } from "zod/v4";

import { LocationSchema } from "@/_shared/common.schema";

export const PlacePredictionSchema = z.object({
  mainText: z.string(),
  placeId: z.string(),
  secondaryText: z.string().nullable(),
  types: z.array(z.string()).nullable(),
});

export const PlacesSearchRequestSchema = z.object({
  input: z.string().min(4).max(100),
  location: LocationSchema,
  radius: z.number().default(5000),
  sessionToken: z.string().min(10).max(50),
});
export const PlacesSearchResponseSchema = z.object({});
export const PlacesSearchQuerySchema = z.object({
  input: z.string().describe("Input string for place search"),
});

export type PlacesSearchRequest = z.infer<typeof PlacesSearchRequestSchema>;
export type PlacesSearchResponse = z.infer<typeof PlacesSearchResponseSchema>;
