import type { FastifyInstance } from "fastify";

import { SuccessResponseSchema } from "@/_shared/common.schema";
import { StatusCodes } from "@/_shared/error";
import { setupInitRoutes } from "@/modules/init/init.routes";
import { setupPlaceRoutes } from "@/modules/places/place.routes";
import { setupRideRoutes } from "@/modules/ride/ride.routes";
import { setupRouteRoutes } from "@/modules/routes/route.routes";
import { setupUserRoutes } from "@/modules/user/user.routes";

export function setupRoutes(app: FastifyInstance) {
  // Health check
  app.get("/healthcheck", {
    config: {
      auth: false,
      headersCheck: false,
    },
    handler: async () => {
      return { success: true };
    },
    schema: {
      response: {
        [StatusCodes.OK]: SuccessResponseSchema,
      },
    },
  });

  // Setup routes for modules
  setupInitRoutes(app);
  setupUserRoutes(app);
  setupRideRoutes(app);
  setupRouteRoutes(app);
  setupPlaceRoutes(app);
}
