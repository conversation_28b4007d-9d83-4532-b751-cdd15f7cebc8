import type { FastifyRequest } from "fastify";

import type { SuccessResponse } from "@/_shared/common.schema";
import type { User, UserAltDetails, UserNotificationToken, UserRequest, UserResponse, UserSettings } from "@/modules/user/user.schema";

import { getClientConfig } from "@/modules/common/config.model";
import { getSunTimes } from "@/modules/common/suntimes.model";
import { isAuthenticated } from "@/modules/common/utils";
import { UserService } from "@/modules/user/user.service";

export async function getUser(req: FastifyRequest): Promise<UserResponse> {
  const { userId } = req.params as { userId: string };
  const userRequest = req.body as UserRequest;
  // Check if userId is is of authenticated user
  const reqUser = isAuthenticated(req, userId);
  // Create user object from authenticated user
  const userObject: User = {
    createdAt: new Date(reqUser.metadata.creationTime).toISOString(),
    email: reqUser.email,
    id: reqUser.uid,
    isAnonymous: false,
    isEmailVerified: reqUser.emailVerified,
    name: reqUser.displayName,
    notificationToken: userRequest.notificationToken,
    phoneNumber: reqUser.phoneNumber,
    photoURL: reqUser.photoURL,
    provider: reqUser.providerData.map(provider => provider.providerId),
    updatedAt: new Date(reqUser.metadata.lastSignInTime).toISOString(),
  } as User;
  // Get user
  const user = await UserService.get(userObject);
  // Get config
  const config = getClientConfig(req.platformName!);
  // Get location grid
  const locationGrid = await getSunTimes(userRequest.lat, userRequest.lng);
  return {
    config: {
      config,
      sunrise: locationGrid.sunrise,
      sunset: locationGrid.sunset,
    },
    user,
  } as UserResponse;
}

// export async function saveUser(req: FastifyRequest): Promise<SuccessResponse> {
//   const user = req.body as User;
//   // Check if user object is of authenticated user
//   isAuthenticated(req, user.id);
//   // Save user
//   await UserService.save(user);
//   return { success: true };
// }

export async function saveSettings(req: FastifyRequest): Promise<SuccessResponse> {
  const { userId } = req.params as { userId: string };
  const settings = req.body as UserSettings;
  // Check if userId is is of authenticated user
  isAuthenticated(req, userId);
  // Save user settings
  await UserService.saveSettings(userId, settings);
  return { success: true };
}

export async function saveNotificationToken(req: FastifyRequest): Promise<SuccessResponse> {
  const { userId } = req.params as { userId: string };
  const token = req.body as UserNotificationToken;
  // Check if userId is is of authenticated user
  isAuthenticated(req, userId);
  // Save notification token
  await UserService.saveNotificationToken(userId, token.token);
  return { success: true };
}

export async function saveAltDetails(req: FastifyRequest): Promise<SuccessResponse> {
  const { userId } = req.params as { userId: string };
  const altDetails = req.body as UserAltDetails;
  // Check if userId is is of authenticated user
  isAuthenticated(req, userId);
  // Save alternate details
  await UserService.saveAltDetails(userId, altDetails);
  return { success: true };
}
