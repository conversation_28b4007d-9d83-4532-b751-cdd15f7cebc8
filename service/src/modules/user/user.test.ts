import type { User } from "@/_testUtils/auth";
import { faker } from "@faker-js/faker";
import { afterEach, beforeAll, beforeEach, describe, expect, it } from "vitest";
import { EmptyTestResponse, testAppInstance } from "@/_testUtils/app";
import { AuthClient } from "@/_testUtils/auth";
import { Cleanup } from "@/_testUtils/cleanup";
import { UserResponseSchema } from "@/modules/user/user.schema";

describe("/user", () => {
  const app = testAppInstance;
  let testResponse = EmptyTestResponse;
  const lat = faker.location.latitude();
  const lng = faker.location.longitude();
  const getUserBody = {
    lat,
    lng,
    notificationToken: "fake-notification-token",
    platform: "ios",
  };
  const authClient = new AuthClient();
  let user: User | null = null;

  beforeAll(async () => {
    app.setAuthToken(await authClient.getToken());
    user = authClient.getUser();
    if (user) {
      await Cleanup.deleteUser(user.uid);
    }
  });

  beforeEach(() => {
    testResponse = EmptyTestResponse;
  });

  afterEach((context) => {
    context.onTestFailed(() => {
      console.debug("API Response for failed test:", JSON.stringify(testResponse, null, 2));
    });
  });

  it("get user (without auth)", async () => {
    const response = await app.inject({
      auth: false,
      body: getUserBody,
      method: "POST",
      path: `/user/${user?.uid}`,
    });
    testResponse = response.json();
    expect(response.statusCode).toBe(401);
  });

  it("get user (invalid auth)", async () => {
    const response = await app.inject({
      auth: "fake",
      body: getUserBody,
      method: "POST",
      path: `/user/${user?.uid}`,
    });
    testResponse = response.json();
    expect(response.statusCode).toBe(401);
  });

  it("get user (invalid user)", async () => {
    const response = await app.inject({
      auth: true,
      body: getUserBody,
      method: "POST",
      path: `/user/123`,
    });
    testResponse = response.json();
    expect(response.statusCode).toBe(401);
  });

  it("get user (invalid input)", async () => {
    const response = await app.inject({
      auth: true,
      body: getUserBody,
      method: "POST",
      path: `/user/123`,
    });
    testResponse = response.json();
    expect(response.statusCode).toBe(401);
  });

  describe("get user (valid)", () => {
    it("first run", async () => {
      const response = await app.inject({
        auth: true,
        body: getUserBody,
        method: "POST",
        path: `/user/${user?.uid}`,
      });
      testResponse = response.json();
      console.error("Response:", JSON.stringify(testResponse, null, 2));
      expect(response.statusCode).toBe(200);
      const parsedData = UserResponseSchema.safeParse(testResponse);
      expect(parsedData.success).toBe(true);
    });

    it("second run", async () => {
      const response = await app.inject({
        auth: true,
        body: getUserBody,
        method: "POST",
        path: `/user/${user?.uid}`,
      });
      testResponse = response.json();
      console.error("Response:", JSON.stringify(testResponse, null, 2));
      expect(response.statusCode).toBe(200);
      const parsedData = UserResponseSchema.safeParse(testResponse);
      expect(parsedData.success).toBe(true);
    });
  });

  it("save user settings (valid)", async () => {
    const settings = {
      homeLocation: {
        lat: 12.928628,
        lng: 77.73435,
      },
      notifications: true,
      shareLocation: true,
    };
    const response = await app.inject({
      auth: true,
      body: settings,
      method: "POST",
      path: `/user/${user?.uid}/settings`,
    });
    testResponse = response.json();
    expect(response.statusCode).toBe(200);
    expect(testResponse.success).toBe(true);
  });

  it("save notification token (valid)", async () => {
    const notificationToken = { token: "1234567890" };
    const response = await app.inject({
      auth: true,
      body: notificationToken,
      method: "POST",
      path: `/user/${user?.uid}/notification-token`,
    });
    testResponse = response.json();
    expect(response.statusCode).toBe(200);
    expect(testResponse.success).toBe(true);
  });

  it("save notification token (null)", async () => {
    const notificationToken = { token: null };
    const response = await app.inject({
      auth: true,
      body: notificationToken,
      method: "POST",
      path: `/user/${user?.uid}/notification-token`,
    });
    testResponse = response.json();
    expect(response.statusCode).toBe(200);
    expect(testResponse.success).toBe(true);
  });

  it("save alt details (valid)", async () => {
    const altDetails = {
      altName: "Test Alt",
      altPhotoURL: "https://example.com/test.jpg",
    };
    const response = await app.inject({
      auth: true,
      body: altDetails,
      method: "POST",
      path: `/user/${user?.uid}/alt`,
    });
    testResponse = response.json();
    expect(response.statusCode).toBe(200);
    expect(testResponse.success).toBe(true);
  });

  it("save alt details (name:null)", async () => {
    const altDetails = {
      altName: null,
      altPhotoURL: "https://example.com/test.jpg",
    };
    const response = await app.inject({
      auth: true,
      body: altDetails,
      method: "POST",
      path: `/user/${user?.uid}/alt`,
    });
    testResponse = response.json();
    expect(response.statusCode).toBe(200);
    expect(testResponse.success).toBe(true);
  });

  it("save alt details (photo:null)", async () => {
    const altDetails = {
      altName: "Test Alt",
      altPhotoURL: null,
    };
    const response = await app.inject({
      auth: true,
      body: altDetails,
      method: "POST",
      path: `/user/${user?.uid}/alt`,
    });
    testResponse = response.json();
    expect(response.statusCode).toBe(200);
    expect(testResponse.success).toBe(true);
  });

  it("save alt details (null)", async () => {
    const altDetails = {
      altName: null,
      altPhotoURL: null,
    };
    const response = await app.inject({
      auth: true,
      body: altDetails,
      method: "POST",
      path: `/user/${user?.uid}/alt`,
    });
    testResponse = response.json();
    expect(response.statusCode).toBe(200);
    expect(testResponse.success).toBe(true);
  });
});
