import type { FastifyRequest } from "fastify";

import type { SuccessResponse } from "@/_shared/common.schema";
import type {
  CreateRideResponse,
  Participant,
  Ride,
  RideRequest,
  Rides,
  ShareLocationRequest,
  ShareLocationResponse,
} from "@/modules/ride/ride.schema";

import { MyError, StatusCodes } from "@/_shared/error";
import { isAuthenticated } from "@/modules/common/utils";
import { RideService } from "@/modules/ride/ride.service";

export async function getRidesByUser(req: FastifyRequest): Promise<Rides> {
  const { userId } = req.params as { userId: string };
  // Check if userId is is of authenticated user
  isAuthenticated(req, userId);
  // Get rides
  const rides = await RideService.findAllByUser(userId);
  /* v8 ignore next 10 */
  if (rides.length === 0) {
    throw new MyError(
      "warn",
      "Rides.api.getRidesByUser",
      "RIDES_NOT_FOUND",
      StatusCodes.NOT_FOUND,
      new Error("Rides not found"),
      { userId },
    );
  }
  return { rides };
}

export async function getRide(req: FastifyRequest): Promise<Ride> {
  const { rideId } = req.params as { rideId: string };
  // Check if request is  authenticated
  isAuthenticated(req);
  // Get ride
  const ride = await RideService.find(rideId);
  return ride;
}

export async function updateRide(req: FastifyRequest): Promise<SuccessResponse> {
  const ride = req.body as RideRequest;
  const { rideId } = req.params as { rideId: string };
  // Check if request is  authenticated
  const userRecord = isAuthenticated(req);
  // Update ride
  await RideService.update(userRecord.uid, rideId, ride);
  return { success: true };
}

export async function createRide(req: FastifyRequest): Promise<CreateRideResponse> {
  const rideRequest = req.body as RideRequest;
  // Check if request is  authenticated
  const userRecord = isAuthenticated(req);
  // Create ride
  const createdRide = await RideService.create(userRecord.uid, rideRequest);
  return { data: { ride: createdRide }, success: true };
}

export async function rsvpRide(req: FastifyRequest): Promise<SuccessResponse> {
  const { rideId } = req.params as { rideId: string };
  const participant = req.body as Participant;
  if (participant.id !== req.user?.uid) {
    throw new MyError(
      "warn",
      "Rides.api.rsvpRide",
      "UNAUTHORIZED",
      StatusCodes.UNAUTHORIZED,
      new Error("Unauthorized"),
      { participantId: participant.id, uid: req.user?.uid },
    );
  }
  await RideService.rsvpRide(rideId, participant);
  return { success: true };
}

export async function shareLocation(req: FastifyRequest): Promise<ShareLocationResponse> {
  const { rideId } = req.params as { rideId: string };
  const location = req.body as ShareLocationRequest;
  const riders = await RideService.shareLocation(
    req.user!.uid,
    rideId,
    location,
  );
  return { riders };
}
