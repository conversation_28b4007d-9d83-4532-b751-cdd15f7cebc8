import { z } from "zod/v4";

import { MyError, StatusCodes } from "@/_shared/error";
import { getDocument, setDocument } from "@/_shared/firebase";
import { HttpClient } from "@/_shared/httpClient";
import { tryCatch } from "@/_shared/tryCatch";

const gridLocationSchema = z.object({
  centerLat: z.number(),
  centerLng: z.number(),
  gridId: z.string(),
});

type GridLocation = z.infer<typeof gridLocationSchema>;

const sunriseSunsetResponseSchema = z.object({
  sunrise: z.string().nullable(),
  sunset: z.string().nullable(),
});

type SunriseSunsetResponse = z.infer<typeof sunriseSunsetResponseSchema>;

const sunriseSunsetDataSchema = sunriseSunsetResponseSchema.extend({
  gridId: z.string(),
  updatedAt: z.string(),
});

type SunriseSunsetData = z.infer<typeof sunriseSunsetDataSchema>;

// Get grid ID and center point for given coordinates
function getGridLocation(lat: number, lng: number): GridLocation {
  /// Constants
  // Grid size in kilometers
  const GRID_SIZE = 200;

  // Calculate grid size in degrees (approximately)
  // At the equator, 1 degree is about 111km
  const gridSizeDegrees = GRID_SIZE / 111;

  // Calculate grid indices
  const latGrid = Math.floor(lat / gridSizeDegrees);
  const lngGrid = Math.floor(lng / gridSizeDegrees);

  // Calculate center of the grid
  const centerLat = latGrid * gridSizeDegrees + gridSizeDegrees / 2;
  const centerLng = lngGrid * gridSizeDegrees + gridSizeDegrees / 2;

  // Create grid ID (encode the grid coordinates)
  const gridId = `${latGrid}:${lngGrid}`;

  return {
    centerLat,
    centerLng,
    gridId,
  };
}

const SuntimesResponseSchema = z.object({
  results: sunriseSunsetResponseSchema,
});
type SuntimesResponse = z.infer<typeof SuntimesResponseSchema>;

// Utility function to fetch sunrise/sunset data
async function fetchSunTimes(lat: number, lng: number): Promise<{ sunrise: string | null; sunset: string | null; updatedAt: string }> {
  const response = await HttpClient.get(
    "https://api.sunrise-sunset.org/json",
    SuntimesResponseSchema,
    {
      date: "today",
      lat,
      lng,
      tzid: "UTC",
    },
  );
  if (response.status !== StatusCodes.OK) {
    /* v8 ignore next 9 */
    throw new MyError(
      "error",
      "LocationGrid.fetchSunTimes",
      "FAILED_TO_FETCH",
      StatusCodes.INTERNAL_SERVER_ERROR,
      response.data,
      { lat, lng },
    );
  }
  const responseData = response.data as SuntimesResponse;
  return {
    sunrise: responseData.results.sunrise,
    sunset: responseData.results.sunset,
    updatedAt: new Date().toISOString(),
  };
}

export async function getSunTimes(lat: number, lng: number): Promise<SunriseSunsetResponse> {
  // Configure cache TTL (time to live)
  const CACHE_TTL = 12 * 60 * 60; // 12 hours in seconds
  const grid = getGridLocation(lat, lng);
  const cachedData = await getDocument(`sun_times/${grid.gridId}`);
  if (cachedData) {
    if (
      cachedData
      && new Date(cachedData.updatedAt).getTime() + CACHE_TTL * 1000
      > Date.now()
    ) {
      /* v8 ignore next 5 */
      return {
        sunrise: cachedData.sunrise,
        sunset: cachedData.sunset,
      } as SunriseSunsetResponse;
    }
  }
  // Call API to fetch sunrise/sunset data
  const sunriseSunset = await tryCatch(
    fetchSunTimes(grid.centerLat, grid.centerLng),
  );
  // In case of API failure, return default values
  if (sunriseSunset.error) {
    return {
      sunrise: null,
      sunset: null,
    } as SunriseSunsetResponse;
  }
  // Update cache
  // Using try catch to ignore cache update errors
  try {
    await setDocument(`sun_times/${grid.gridId}`, {
      gridId: grid.gridId,
      sunrise: sunriseSunset.result.sunrise,
      sunset: sunriseSunset.result.sunset,
      updatedAt: new Date().toISOString(),
    } as SunriseSunsetData);
  }
  catch {
    // Ignore error
  }
  return {
    sunrise: sunriseSunset.result.sunrise,
    sunset: sunriseSunset.result.sunset,
  } as SunriseSunsetResponse;
}
