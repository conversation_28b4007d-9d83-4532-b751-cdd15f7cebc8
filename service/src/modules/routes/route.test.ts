import type { User } from "@/_testUtils/auth";
import { afterEach, beforeAll, beforeEach, describe, expect, it } from "vitest";
import { EmptyTestResponse, testAppInstance } from "@/_testUtils/app";
import { AuthClient } from "@/_testUtils/auth";
import { Cleanup } from "@/_testUtils/cleanup";

describe("/route", () => {
  const app = testAppInstance;
  let testResponse = EmptyTestResponse;
  const authClient = new AuthClient();
  let user: User | null = null;

  beforeAll(async () => {
    app.setAuthToken(await authClient.getToken());
    user = authClient.getUser();
    if (user) {
      await Cleanup.deleteUser(user.uid);
    }
  });

  beforeEach(() => {
    testResponse = EmptyTestResponse;
  });

  afterEach((context) => {
    context.onTestFailed(() => {
      console.debug("API Response for failed test:", JSON.stringify(testResponse, null, 2));
    });
  });

  it("route (without auth)", async () => {
    const response = await app.inject({
      auth: false,
      body: {},
      method: "GET",
      path: `/route/Fswr3pXUaj29unaSZbDue/to`,
    });
    testResponse = response.json();
    expect(response.statusCode).toBe(401);
  });

  it("route (not found)", async () => {
    const response = await app.inject({
      auth: true,
      body: {},
      method: "GET",
      path: `/route/fake-ride-id/to`,
    });
    testResponse = response.json();
    expect(response.statusCode).toBe(404);
  });

  it("route (found)", async () => {
    const response = await app.inject({
      auth: true,
      body: {},
      method: "GET",
      path: `/route/Fswr3pXUaj29unaSZbDue/to`,
    });
    testResponse = response.json();
    expect(response.statusCode).toBe(200);
    expect(testResponse).toHaveProperty("route");
  });
});
