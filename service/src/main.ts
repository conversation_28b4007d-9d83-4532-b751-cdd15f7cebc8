import { config } from "@/_shared/config";
import { buildApp, stopServer } from "@/_shared/fastify";

async function startServer() {
  const app = await buildApp();
  console.info("RUNTIME_ENV:", config.env);
  try {
    await app.listen({ host: config.host, port: config.port });
  }
  catch (err) {
    console.error(`Server failed to start: ${JSON.stringify(err)}`);
    process.exit(1);
  }
  process.on("SIGINT", () => stopServer(app));
  process.on("SIGTERM", () => stopServer(app));
}

startServer();
