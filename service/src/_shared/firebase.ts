import type { DocumentData } from "firebase-admin/firestore";

import admin from "firebase-admin";
import { UserRecord } from "firebase-admin/auth";
import {
  FieldPath,
  FieldValue,
  Timestamp,
} from "firebase-admin/firestore";

import { config } from "@/_shared/config";
import { MyError, StatusCodes } from "@/_shared/error";
import { tryCatch, tryCatchSync } from "@/_shared/tryCatch";

export { FieldPath, FieldValue, Timestamp, UserRecord };
export type { DocumentData };

const setFirestoreOptions = { merge: true };
// Set firestore config as per environment
const firestoreConfig: admin.firestore.Settings = config.useEmulator
  ? {
      cacheSizeBytes: 0,
      host: "127.0.0.1",
      ignoreUndefinedProperties: true,
      port: 8080,
      ssl: false,
    }
  : {
      cacheSizeBytes: 0,
      ignoreUndefinedProperties: true,
      ssl: true,
    };

// Initialize Admin
admin.initializeApp();

// Use emulator for Firebase Auth
// This can be done using environment variable: FIREBASE_AUTH_EMULATOR_HOST

// Initialize Firestore
const firestore = admin.firestore();
firestore.settings(firestoreConfig);

// Export Firestore
export const getFirestore = () => firestore;

// Export Auth
export const getAuth = () => admin.auth();

// Export Storage
export const getStorage = () => admin.storage();

// Export Messaging
export const getMessaging = () => admin.messaging();

// Export wrapper function to fetch collection from Firestore
export async function getCollection(
  collectionPath: string,
  query?: (
    ref: FirebaseFirestore.CollectionReference,
  ) => FirebaseFirestore.Query,
): Promise<DocumentData[]> {
  const collection = tryCatchSync(() => firestore.collection(collectionPath));
  if (collection.error) {
    throw new MyError(
      "error",
      "firebase.getCollection",
      "FIRESTORE_ERROR",
      StatusCodes.INTERNAL_SERVER_ERROR,
      collection.error,
      { collectionPath, query },
    );
  }
  let querySnapshot: FirebaseFirestore.QuerySnapshot;
  if (query) {
    const data = await tryCatch(query(collection.result).get());
    if (data.error) {
      throw new MyError(
        "error",
        "firebase.getCollection",
        "FIRESTORE_ERROR",
        StatusCodes.INTERNAL_SERVER_ERROR,
        data.error,
        { collectionPath, query },
      );
    }
    querySnapshot = data.result;
  }
  else {
    const data = await tryCatch(collection.result.get());
    if (data.error) {
      throw new MyError(
        "error",
        "firebase.getCollection",
        "FIRESTORE_ERROR",
        StatusCodes.INTERNAL_SERVER_ERROR,
        data.error,
        { collectionPath, query },
      );
    }
    querySnapshot = data.result;
  }

  return querySnapshot.docs.map(doc => doc.data() as DocumentData);
}

// Export wrapper function to fetch document from Firestore
export async function getDocument(
  documentPath: string,
): Promise<DocumentData | undefined> {
  const docRef = tryCatchSync(() => firestore.doc(documentPath));
  if (docRef.error) {
    throw new MyError(
      "error",
      "firebase.getDocument",
      "FIRESTORE_ERROR",
      StatusCodes.INTERNAL_SERVER_ERROR,
      docRef.error,
      { documentPath },
    );
  }
  const docSnapshot = await tryCatch(docRef.result.get());
  if (docSnapshot.error) {
    throw new MyError(
      "error",
      "firebase.getDocument",
      "FIRESTORE_ERROR",
      StatusCodes.INTERNAL_SERVER_ERROR,
      docSnapshot.error,
      { documentPath },
    );
  }
  const docData = tryCatchSync(() => docSnapshot.result.data());
  if (docData.error) {
    return undefined;
  }
  return docData.result;
}

// Export wrapper function to set document in Firestore
export async function setDocument(
  documentPath: string,
  data: DocumentData,
): Promise<admin.firestore.WriteResult> {
  const document = tryCatchSync(() => firestore.doc(documentPath));
  if (document.error) {
    throw new MyError(
      "error",
      "firebase.setDocument",
      "FIRESTORE_ERROR",
      StatusCodes.INTERNAL_SERVER_ERROR,
      document.error,
      { data, documentPath, setFirestoreOptions },
    );
  }
  const set = await tryCatch(document.result.set(data, setFirestoreOptions));
  if (set.error) {
    throw new MyError(
      "error",
      "firebase.setDocument",
      "FIRESTORE_ERROR",
      StatusCodes.INTERNAL_SERVER_ERROR,
      set.error,
      { data, documentPath, setFirestoreOptions },
    );
  }
  return set.result;
}

// Export wrapper function to update document in Firestore
export async function updateDocument(
  documentPath: string,
  data: DocumentData,
): Promise<admin.firestore.WriteResult> {
  const document = tryCatchSync(() => firestore.doc(documentPath));
  if (document.error) {
    throw new MyError(
      "error",
      "firebase.updateDocument",
      "FIRESTORE_ERROR",
      StatusCodes.INTERNAL_SERVER_ERROR,
      document.error,
      { data, documentPath },
    );
  }
  const update = await tryCatch(document.result.update(data));
  if (update.error) {
    throw new MyError(
      "error",
      "firebase.updateDocument",
      "FIRESTORE_ERROR",
      StatusCodes.INTERNAL_SERVER_ERROR,
      update.error,
      { data, documentPath },
    );
  }
  return update.result;
}

/**
 * Recursively converts all Date objects in an object to Firestore Timestamps
 * @param obj - The object to process
 * @returns A new object with all Date objects converted to Timestamps
 */
export function convertDatesToTimestamps<T>(obj: T): T {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (obj instanceof Date) {
    return Timestamp.fromDate(obj) as unknown as T;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => convertDatesToTimestamps(item)) as unknown as T;
  }

  if (typeof obj === "object") {
    const result: Record<string, unknown> = {};

    for (const [key, value] of Object.entries(obj)) {
      result[key] = convertDatesToTimestamps(value);
    }

    return result as T;
  }

  return obj;
}

/**
 * Recursively converts all Firestore Timestamps in an object to JavaScript Date objects
 * @param obj - The object to process
 * @returns A new object with all Timestamps converted to Date objects
 */
export function convertTimestampsToDates<T>(obj: T): T {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (obj instanceof Timestamp) {
    return obj.toDate().toISOString() as unknown as T;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => convertTimestampsToDates(item)) as unknown as T;
  }

  if (typeof obj === "object") {
    const result: Record<string, unknown> = {};

    for (const [key, value] of Object.entries(obj)) {
      result[key] = convertTimestampsToDates(value);
    }

    return result as T;
  }

  return obj;
}
