import { config } from "@/_shared/config";
import type { LoggerOptions, TransportTargetOptions } from "pino";
import pino from "pino";

const _localTransportOptions: TransportTargetOptions = {
  options: {
    colorize: true,
    colorizeObjects: true,
    ignore: "pid,hostname",
    levelFirst: true,
    singleLine: true,
    translateTime: "SYS:hh:MM:ss tt",
  },
  target: "pino-pretty",
};
const _remoteTransportOptions: TransportTargetOptions = {
  options: {
    colorize: false,
    colorizeObjects: false,
    levelFirst: true,
    singleLine: false,
  },
  target: "pino/file",
};

export const loggerConfig: LoggerOptions = {
  formatters: {
    level: (label) => {
      return {
        level: label.toUpperCase(),
      };
    },
  },
  level: config.logLevel,
  transport: config.isEnvLocal || config.isEnvTest ? _localTransportOptions : _remoteTransportOptions,
};

export const logger = pino(loggerConfig);
// Redirect all console methods to use Fastify's logger
// console.log = (...args) => logger.info(args.join(' '));
console.error = (...args) => logger.error(args.join(" "));
console.warn = (...args) => logger.warn(args.join(" "));
console.info = (...args) => logger.info(args.join(" "));
console.debug = (...args) => logger.debug(args.join(" "));
