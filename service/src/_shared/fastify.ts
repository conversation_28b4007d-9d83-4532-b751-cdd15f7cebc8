import type { FastifyHttp2Options, FastifyHttp2SecureOptions, FastifyInstance, FastifyServerOptions } from "fastify";
import type { UserRecord } from "firebase-admin/auth";

import type { PlatformName, PlatformType } from "@/_shared/common.schema";
import cors from "@fastify/cors";
import helmet from "@fastify/helmet";
import { readFileSync } from "node:fs";

import { authPlugin } from "@/_middleware/auth.plugin";
import { customRouteOptions } from "@/_middleware/custom.route.options";
import { errorHandler } from "@/_middleware/error.handler";
import { headerValidationPlugin } from "@/_middleware/headers.plugin";
import { config } from "@/_shared/config";
import { loggerConfig } from "@/_shared/logger";
import { setupRoutes } from "@/modules/routes";
import Fastify from "fastify";
import {
  serializerCompiler,
  validatorCompiler,
} from "fastify-type-provider-zod";

export type { FastifyInstance };

// Extend Fastify types
declare module "fastify" {
  // eslint-disable-next-line ts/consistent-type-definitions
  interface FastifyRequest {
    user?: UserRecord;
    platformName?: PlatformName;
    platformType?: PlatformType;
    traceId?: string;
    timeZoneName?: string;
    timeZoneOffset?: number;
  }

  // eslint-disable-next-line ts/consistent-type-definitions
  interface RouteShorthandOptions {
    auth?: boolean;
  }
}

export async function buildApp(): Promise<FastifyInstance> {
  // Configure server options
  const serverOptions: FastifyServerOptions = {
    bodyLimit: 10_240, // 10KB
    disableRequestLogging: !config.isEnvLocal,
    logger: loggerConfig,
    requestTimeout: 30_000, // 30 seconds
  };

  // Add HTTP/2 and HTTPS configuration if enabled
  if (config.http2 && config.https && config.sslKey && config.sslCert) {
    try {
      (serverOptions as FastifyHttp2Options<any>).http2 = true;
      (serverOptions as FastifyHttp2SecureOptions<any>).https = {
        allowHTTP1: true,
        cert: readFileSync(config.sslCert),
        key: readFileSync(config.sslKey),
      };
      console.info("✅ HTTP/2 with HTTPS enabled");
    }
    catch (error) {
      console.warn("⚠️  Failed to load SSL certificates, falling back to HTTP/1.1:", error);
    }
  }
  else if (config.http2 && !config.https) {
    // HTTP/2 over cleartext (h2c) - mainly for development behind a proxy
    (serverOptions as FastifyHttp2Options<any>).http2 = true;
    console.info("✅ HTTP/2 over cleartext (h2c) enabled");
  }
  else {
    console.info("ℹ️  Using HTTP/1.1");
  }

  const app = Fastify(serverOptions);

  // Set custom route options
  await app.register(customRouteOptions);

  // Register error handler
  app.setErrorHandler(errorHandler);

  // Register plugins
  await app.register(cors, {
    credentials: true,
    methods: ["GET", "POST"],
    origin: (origin, cb) => {
      // Allow requests with no origin (like mobile apps)
      if (!origin) {
        return cb(null, true);
      }

      // Check if the origin is in our allowed list
      const allowedOrigins = ["https://95octane.app", "https://95octane.dev"];
      if (allowedOrigins.includes(origin)) {
        return cb(null, true);
      }

      // Otherwise, deny the request
      return cb(new Error("Not allowed by CORS"), false);
    },
  });

  await app.register(helmet, {
    global: true,
  });

  // Register type provider
  app.setSerializerCompiler(serializerCompiler);
  app.setValidatorCompiler(validatorCompiler);

  // Register Auth plugin
  await app.register(authPlugin);

  // Register Headers plugin
  await app.register(headerValidationPlugin);

  // Register routes
  setupRoutes(app);

  return app;
}

export async function stopServer(app: FastifyInstance): Promise<void> {
  console.info("Stopping server...");
  await app.close();
  console.info("Server stopped successfully.");
  process.exit(0);
}
