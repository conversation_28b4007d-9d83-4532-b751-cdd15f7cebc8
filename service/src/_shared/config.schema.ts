import { z } from "zod/v4";

export const ConfigSchema = z.object({
  env: z.enum(["development", "production", "test", "local"]).default("production"),
  googleApplicationCredentials: z.string().optional(),
  host: z.string().default("0.0.0.0"),
  http2: z.boolean().default(true),
  https: z.boolean().default(false),
  logLevel: z.enum(["silent", "error", "warn", "info", "debug"]).default("warn"),
  minAppVersion: z.number().int().min(1).default(1),
  port: z.number().default(8888),
  sslCert: z.string().optional(),
  sslKey: z.string().optional(),
  useEmulator: z.boolean().default(false),
})
.strict()
.transform((config) => ({
  ...config,
  isEnvDevelopment: config.env === "development",
  isEnvTest: config.env === "test",
  isEnvLocal: config.env === "local",
  isEnvProduction: config.env === "production",
}));
