/* eslint-disable node/no-process-env */
import { ConfigSchema } from "@/_shared/config.schema";
import { z } from "zod/v4";

const parsed = ConfigSchema.safeParse({
  env: process.env.RUNTIME_ENV,
  googleApplicationCredentials: process.env.GOOGLE_APPLICATION_CREDENTIALS,
  host: process.env.HOST,
  http2: process.env.HTTP2 !== "false", // Default to true unless explicitly disabled
  https: process.env.HTTPS === "true",
  logLevel: process.env.LOG_LEVEL || "warn",
  minAppVersion:
    process.env.MINIMUM_APP_VERSION
      ? Number.parseInt(process.env.MINIMUM_APP_VERSION, 10)
      : 1,
  port: process.env.PORT ? Number.parseInt(process.env.PORT, 10) : undefined,
  sslCert: process.env.SSL_CERT,
  sslKey: process.env.SSL_KEY,
  useEmulator: process.env.USE_EMULATOR === "true",
});

if (!parsed.success) {
  console.error("❌ Invalid environment variables:", z.prettifyError(parsed.error));
  process.exit(1);
}

export const config = parsed.data;
