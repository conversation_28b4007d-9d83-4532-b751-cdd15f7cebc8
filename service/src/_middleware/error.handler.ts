import type { FastifyReply, FastifyRequest } from "fastify";

import type { ErrorResponse } from "@/_shared/common.schema";

import { ZodError } from "zod/v4";

import { config } from "@/_shared/config";
import { MyError, StatusCodes } from "@/_shared/error";

export function errorHandler(
  error: unknown,
  request: FastifyRequest,
  reply: FastifyReply,
) {
  // Extract request data for context
  const traceId = request.headers["x-trace-id"] as string | undefined;
  const requestId = request.id;
  const method = request.method;
  const url = request.url;
  const ip = request.ip;

  // Normalize the error to a MyError instance
  let normalizedError: MyError;
  if (error instanceof MyError) {
    normalizedError = error;
  }
  else if (error instanceof ZodError) {
    normalizedError = MyError.fromError(
      error,
      "error",
      "ParserError",
      "PARSER_ERROR",
      StatusCodes.BAD_REQUEST,
      { method, requestId, traceId, url },
    );
  }
  else {
    // Convert unknown errors to MyError format
    normalizedError = MyError.fromError(
      error,
      "error",
      "UnhandledError",
      "INTERNAL_ERROR",
      StatusCodes.INTERNAL_SERVER_ERROR,
      { method, requestId, traceId, url },
    );
  }

  // Add request context to the error if not already present
  if (!normalizedError.ctx.traceId && traceId) {
    normalizedError.ctx.traceId = traceId;
  }
  if (!normalizedError.ctx.requestId) {
    normalizedError.ctx.requestId = requestId;
  }
  if (!normalizedError.ctx.requestInfo) {
    normalizedError.ctx.requestInfo = { ip, method, url };
  }

  // Create structured log entry
  const logEntry = {
    context: normalizedError.ctx,
    errorCode: normalizedError.code,
    errorName: normalizedError.name,
    message: normalizedError.message,
    method,
    requestId,
    stack: normalizedError.stack,
    statusCode: normalizedError.statusCode,
    timestamp: new Date().toISOString(),
    traceId,
    url,
  };

  const logMessage
    = config.isEnvDevelopment ? normalizedError.toString() : JSON.stringify(logEntry);

  // Log based on error severity
  switch (normalizedError.errorType) {
    case "error":
      console.error(logMessage);
      break;
    case "warn":
      console.warn(logMessage);
      break;
    case "info":
      console.info(logMessage);
      break;
    default:
      console.error(logMessage);
  }

  // Prepare client response - don't expose sensitive details
  const clientResponse = {
    code: normalizedError.code,
    message: normalizedError.message,
    stack: config.isEnvDevelopment || config.isEnvTest ? normalizedError.stack : undefined,
    status: normalizedError.statusCode,
  } as ErrorResponse;

  return reply
    .status(normalizedError.statusCode)
    .header("X-Error-Code", normalizedError.code)
    .send(clientResponse);
}
