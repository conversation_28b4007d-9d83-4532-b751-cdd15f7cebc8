import type { FastifyInstance, FastifyPluginAsync } from "fastify";

import type { CustomRouteOptions } from "@/_middleware/custom.types";

import fp from "fastify-plugin";

export const customRouteOptions: FastifyPluginAsync = fp(
  async (fastify: FastifyInstance) => {
    // Add auth decorator to all routes by default
    fastify.addHook("onRoute", (routeOptions: CustomRouteOptions) => {
      // Set auth to true by default if not explicitly set
      if (routeOptions.auth === undefined) {
        routeOptions.auth = true;
      }
      if (routeOptions.headersCheck === undefined) {
        routeOptions.headersCheck = true;
      }
    });
  },
);
