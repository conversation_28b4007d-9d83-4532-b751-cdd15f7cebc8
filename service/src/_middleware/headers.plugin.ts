import type { FastifyInstance, FastifyPluginAsync } from "fastify";

import type { CustomRouteOptions } from "@/_middleware/custom.types";

import fp from "fastify-plugin";
import { HeadersSchema } from "@/_shared/common.schema";
import { config } from "@/_shared/config";
import { MyError, StatusCodes } from "@/_shared/error";

export const headerValidationPlugin: FastifyPluginAsync = fp(
  async (fastify: FastifyInstance) => {
    // Add a hook to validate headers before handling the request
    fastify.addHook("onRequest", async (req) => {
      // Skip header checking for routes that don't need it
      const routeConfig = req.routeOptions.config as CustomRouteOptions;
      if (routeConfig.headersCheck === false)
        return;

      // Validate headers on every request
      const parsed = HeadersSchema.safeParse(req.headers);
      if (!parsed.success) {
        throw new MyError(
          "error",
          "headerValidationPlugin",
          "BAD_REQUEST",
          StatusCodes.BAD_REQUEST,
          parsed.error,
          {},
        );
      }
      if (
        parsed.data
        && parsed.data["x-app-version"] < config.minAppVersion
      ) {
        throw new MyError(
          "error",
          "headerValidationPlugin",
          "UPGRADE_REQUIRED",
          StatusCodes.UPGRADE_REQUIRED,
          "Please upgrade the app",
          {
            "x-app-version": parsed.data["x-app-version"],
          },
        );
      }
      // Set the parsed headers back to the request object
      req.platformName = parsed.data["x-platform-name"];
      req.traceId = parsed.data["x-trace-id"];
      req.timeZoneName = parsed.data["x-tz-name"];
      req.timeZoneOffset = parsed.data["x-tz-offset"];
    });
  },
);
